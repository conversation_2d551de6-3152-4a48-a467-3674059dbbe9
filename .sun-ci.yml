workspace: true

stages:
  - ktlint
  - detekt

jobs:
  - name: Ktlint checking
    stage: ktlint
    image: amazoncorretto:17.0.10
    cache:
      - key: jars-{{ checksum "build.gradle" }}-{{ checksum  "app/build.gradle" }}
        paths:
          - ./gradle
    before_script:
      - chmod +x ./gradlew
      - echo "failed (pray)(sweat);(" > .build_status
      - echo "(devil)" > .build_icon
    script:
      - ./gradlew ktlintCheck --daemon
      - status1=$?
      - if [[ "$status1" = 0 ]] ; then
      - echo "*******************************************************"
      - echo "             Ktlint runs successfully                  " > .build_status
      - echo "                    (handshake)                        " > .build_icon
      - echo "*******************************************************"
      - else
      - echo "*******************************************************"
      - echo "                 Ktlint failed                         "
      - echo "     Please fix the reported issues before commit.     "
      - echo "*******************************************************"
      - exit status1
      - fi
    only:
      branches:
        - develop
        - main
        - master
      events:
        - pull_request

  - name: Detekt checking
    stage: detekt
    image: amazoncorretto:17.0.10
    script:
      - ./gradlew detekt --daemon
      - status2=$?
      - if [[ "$status2" = 0 ]] ; then
      - echo "*******************************************************"
      - echo "             Detekt runs successfully                  " > .build_status
      - echo "                    (handshake)                        " > .build_icon
      - echo "*******************************************************"
      - else
      - echo "*******************************************************"
      - echo "                 Detekt failed                         "
      - echo "     Please fix the reported issues before commit.     "
      - echo "*******************************************************"
      - exit status2
      - fi
    only:
      branches:
        - develop
        - main
        - master
      events:
        - pull_request
