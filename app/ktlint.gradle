configurations {
    ktlint
}

tasks.register('ktlint<PERSON><PERSON><PERSON>', JavaExec) {
    group = 'verification'
    description = 'Check Kotlin code style.'
    classpath = configurations.ktlint
    main = 'com.pinterest.ktlint.Main'
    args "src/**/*.kt"
}

tasks.register('ktlintFormat', JavaExec) {
    group = 'formatting'
    description = 'Fix Kotlin code style deviations.'
    classpath = configurations.ktlint
    main = 'com.pinterest.ktlint.Main'
    args '-F', 'src/**/*.kt'
}

dependencies {
    ktlint "com.pinterest:ktlint:0.36.0"
}
