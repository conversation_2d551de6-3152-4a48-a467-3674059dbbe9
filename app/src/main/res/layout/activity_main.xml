<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <Button
        android:id="@+id/btnNavigateToSampleMVP"
        android:layout_width="@dimen/dp_0"
        android:layout_height="wrap_content"
        android:text="Open SampleActivity (MVP)"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_margin="@dimen/dp_16"/>

    <Button
        android:id="@+id/btnShowFragment"
        android:layout_width="@dimen/dp_0"
        android:layout_height="wrap_content"
        android:text="Show ExampleFragment"
        app:layout_constraintTop_toBottomOf="@id/btnNavigateToSampleMVP"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_margin="@dimen/dp_16"/>

    <androidx.fragment.app.FragmentContainerView
        android:id="@+id/fragment_container"
        android:layout_width="@dimen/dp_0"
        android:layout_height="@dimen/dp_0"
        app:layout_constraintTop_toBottomOf="@id/btnShowFragment"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>
