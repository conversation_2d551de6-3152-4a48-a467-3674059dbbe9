package com.sun.nhom6.example.fragment

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.sun.nhom6.databinding.ItemTeamBinding

class MyTeamsAdapter :
    ListAdapter<String, MyTeamsAdapter.TeamVH>(DIFF) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) =
        TeamVH(ItemTeamBinding.inflate(
            LayoutInflater.from(parent.context), parent, false))

    override fun onBindViewHolder(holder: TeamVH, position: Int) =
        holder.bind(getItem(position))

    inner class TeamVH(private val binding: ItemTeamBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(name: String) {
            binding.tvTeamName.text = name
        }
    }

    private companion object {
        val DIFF = object : DiffUtil.ItemCallback<String>() {
            override fun areItemsTheSame(oldItem: String, newItem: String) = oldItem == newItem
            override fun areContentsTheSame(oldItem: String, newItem: String) = oldItem == newItem
        }
    }
}
