package com.sun.nhom6.example

import com.sun.nhom6.base.BasePresenter
import com.sun.nhom6.data.repository.ITeamRepository
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class ExamplePresenter(
    private val teamRepository: ITeamRepository
) : BasePresenter<ExampleContract.View>(), ExampleContract.Presenter {

    override fun loadMyTeams() {
        presenterScope.launch {
            val teams = try {
                withContext(Dispatchers.IO) {
                    teamRepository.getMyTeams()
                }
            } catch (e: Exception) {
                view?.showError("Failed to load teams: ${e.message}")
                return@launch
            }
            view?.showMyTeams(teams)
        }
    }
}
