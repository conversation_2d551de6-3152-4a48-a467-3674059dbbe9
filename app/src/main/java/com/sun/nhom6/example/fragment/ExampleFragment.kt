package com.sun.nhom6.example.fragment

import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.Toast
import androidx.recyclerview.widget.LinearLayoutManager
import com.sun.nhom6.base.BaseFragment
import com.sun.nhom6.data.repository.TeamRepository
import com.sun.nhom6.databinding.FragmentExampleBinding
import com.sun.nhom6.example.ExampleContract
import com.sun.nhom6.example.ExamplePresenter

class ExampleFragment :
    BaseFragment<
            FragmentExampleBinding,
            ExampleContract.View,
            ExamplePresenter>(),
    ExampleContract.View {

    private val repository by lazy { TeamRepository() }
    private val teamsAdapter = MyTeamsAdapter()

    override fun inflateBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): FragmentExampleBinding =
        FragmentExampleBinding.inflate(inflater, container, false)

    override fun providePresenter(): ExamplePresenter =
        ExamplePresenter(repository)

    override fun setupViews() {
        binding.rvTeams.apply {
            adapter = teamsAdapter
            layoutManager = LinearLayoutManager(requireContext())
        }
    }

    override fun setupListeners() {
        binding.btnLoad.setOnClickListener { presenter.loadMyTeams() }
    }

    override fun showMyTeams(teams: List<String>) {
        teamsAdapter.submitList(teams)
    }

    override fun showLoading() {
        Toast.makeText(requireContext(), "Loading teams…", Toast.LENGTH_SHORT).show()
    }

    override fun hideLoading() { /* no–op */ }

    override fun showError(message: CharSequence?) {
        Toast.makeText(requireContext(), "Error: $message", Toast.LENGTH_LONG).show()
    }
}
