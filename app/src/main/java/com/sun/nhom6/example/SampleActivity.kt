package com.sun.nhom6.example

import android.view.LayoutInflater
import android.widget.Toast
import com.sun.nhom6.base.BaseActivity
import com.sun.nhom6.data.repository.TeamRepository
import com.sun.nhom6.databinding.ActivitySampleBinding

class SampleActivity :
    BaseActivity<ActivitySampleBinding, ExampleContract.View, ExamplePresenter>(),
    ExampleContract.View {

    override fun inflateBinding(inflater: LayoutInflater): ActivitySampleBinding =
        ActivitySampleBinding.inflate(inflater)

    private val repository by lazy { TeamRepository() }

    override fun providePresenter(): ExamplePresenter =
        ExamplePresenter(repository)

    override fun setupViews() {
        // Init UI (toolbar, adapter, ...)
    }

    override fun setupListeners() {
        binding.loadMyTeamsButton.setOnClickListener {
            presenter.loadMyTeams()
        }
    }

    override fun showMyTeams(teams: List<String>) {
        Toast.makeText(this, teams.joinToString(", "), Toast.LENGTH_LONG).show()
    }

    override fun showLoading() {
        Toast.makeText(this, "Loading teams…", Toast.LENGTH_SHORT).show()
    }

    override fun hideLoading() {}

    override fun showError(message: CharSequence?) {
        Toast.makeText(this, "Error: $message", Toast.LENGTH_LONG).show()
    }
}
