package com.sun.nhom6.base

import android.os.Bundle
import android.view.LayoutInflater
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.viewbinding.ViewBinding

abstract class BaseActivity<
        VB : ViewBinding,
        V : BaseContract.View,
        P : BasePresenter<V>
        > : AppCompatActivity(), BaseContract.View {

    private var _binding: VB? = null
    protected val binding get() = _binding!!
    protected lateinit var presenter: P
        private set

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        _binding = inflateBinding(layoutInflater)
        setContentView(binding.root)

        presenter = providePresenter()
        @Suppress("UNCHECKED_CAST")
        presenter.attachView(this as V)

        setupViews()
        setupListeners()
    }

    override fun onDestroy() {
        presenter.detachView()
        _binding = null
        super.onDestroy()
    }

    open override fun showLoading() {}
    open override fun hideLoading() {}
    open override fun showError(message: CharSequence?) {
        Toast.makeText(this, message ?: "Unknown error", Toast.LENGTH_SHORT).show()
    }

    protected abstract fun inflateBinding(inflater: LayoutInflater): VB
    protected abstract fun providePresenter(): P
    protected open fun setupViews() {}
    protected open fun setupListeners() {}
}
