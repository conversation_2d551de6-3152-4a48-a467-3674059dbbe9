package com.sun.nhom6.base

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.viewbinding.ViewBinding

abstract class BaseFragment<VB : ViewBinding, V : BaseContract.View, P : BasePresenter<V>> :
    Fragment(), BaseContract.View {

    private var _binding: VB? = null
    protected val binding: VB
        get() = _binding!!

    protected lateinit var presenter: P

    final override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = inflateBinding(inflater, container)
        return binding.root
    }

    final override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        presenter = providePresenter()
        @Suppress("UNCHECKED_CAST") presenter.attachView(this as V)

        setupViews()
        setupListeners()
    }

    final override fun onDestroyView() {
        presenter.detachView()
        _binding = null
        super.onDestroyView()
    }

    open override fun showLoading() {}
    open override fun hideLoading() {}
    open override fun showError(message: CharSequence?) {}

    protected abstract fun inflateBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): VB

    protected abstract fun providePresenter(): P

    protected open fun setupViews() {}

    protected open fun setupListeners() {}
}
