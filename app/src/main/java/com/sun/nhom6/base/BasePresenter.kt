package com.sun.nhom6.base

import java.lang.ref.WeakReference
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob

abstract class BasePresenter<V : BaseContract.View> : BaseContract.Presenter<V> {

    private var viewRef: WeakReference<V>? = null

    private var job = SupervisorJob()
    protected var presenterScope: CoroutineScope = CoroutineScope(job + Dispatchers.Main.immediate)

    override fun attachView(view: V) {
        viewRef = WeakReference(view)
        // If the job/scope was previously cancelled, recreate it for reuse.
        if (!job.isActive) {
            job = SupervisorJob()
            presenterScope = CoroutineScope(job + Dispatchers.Main.immediate)
        }
    }

    override fun detachView() {
        viewRef?.clear()
        viewRef = null
        // Cancel the scope to prevent coroutine leaks.
        job.cancel()
    }

    protected val view: V?
        get() = viewRef?.get()

    protected val isViewAttached: <PERSON>ole<PERSON>
        get() = view != null
}
