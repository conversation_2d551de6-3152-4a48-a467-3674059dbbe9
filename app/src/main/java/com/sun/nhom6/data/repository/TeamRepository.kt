package com.sun.nhom6.data.repository

import kotlinx.coroutines.delay

interface ITeamRepository {
    suspend fun getMyTeams(): List<String>
}

class TeamRepository : ITeamRepository {
    override suspend fun getMyTeams(): List<String> {
        // TODO: remove later
        delay(1000)
        return listOf(
            "Vũ Bảo Long",
            "Nguyễn <PERSON>",
            "Nguyễn <PERSON>",
            "Nguyễn <PERSON>"
        )
    }
}
