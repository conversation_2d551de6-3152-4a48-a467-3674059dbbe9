package com.sun.nhom6

import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.sun.nhom6.databinding.ActivityMainBinding
import com.sun.nhom6.example.SampleActivity // vẫn dùng
import com.sun.nhom6.example.fragment.ExampleFragment

class MainActivity : AppCompatActivity() {

    private val binding by lazy { ActivityMainBinding.inflate(layoutInflater) }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)

        binding.btnNavigateToSampleMVP.setOnClickListener {
            startActivity(Intent(this, SampleActivity::class.java))
        }

        binding.btnShowFragment.setOnClickListener {
            supportFragmentManager.beginTransaction()
                .replace(R.id.fragment_container, ExampleFragment())
                .addToBackStack(null)
                .commit()
        }
    }
}
