// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    apply from: "$rootDir/team-props/git-hooks.gradle"

    repositories {
        maven {
            url "https://plugins.gradle.org/m2/"
        }
        google()
        mavenCentral()
    }
}

plugins {
    id 'com.android.application' version '8.0.2' apply false
    id 'com.android.library' version '8.0.2' apply false
    id 'org.jetbrains.kotlin.android' version '1.8.20' apply false
}

tasks.register('clean', Delete) {
    delete rootProject.buildDir
}
